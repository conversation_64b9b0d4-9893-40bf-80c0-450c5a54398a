import {
  DoDisturb,
  Sync,
  LockOutlined,
  PendingActionsOutlined,
} from '@mui/icons-material';
import {
  Box,
  Grid,
  Tooltip,
  Typography,
  createTheme,
  ThemeProvider,
} from '@mui/material';
import orderBy from 'lodash/orderBy';
import React, { FC, useEffect, useMemo, useState } from 'react';
import { RJSFSchema, RJSFValidationError, StrictRJSFSchema } from '@rjsf/utils';

import Form from '@rjsf/mui';
import validator from '@rjsf/validator-ajv8';
import { IChangeEvent } from '@rjsf/core';
import { useMergeState } from '../../hooks/useMergeStates';
import { ICustomAttributeDefinitionWithStaged } from '../../constants/types';
import { useDebounce } from '../../hooks/useDebounce';

interface IProps {
  attributes: ICustomAttributeDefinitionWithStaged[];
  customAttributeIds?: string[];
  isBulkUpdate?: boolean;
  onValueUpdate: (
    definitionId,
    value,
    isValid: boolean,
    isUpdated: boolean
  ) => void;
  disableForm: boolean;
}

const customStyles = {
  ellipsis: {
    '& .MuiTypography-root': {
      whiteSpace: 'nowrap',
      minWidth: '0px',
      overflow: 'hidden',
      textOverflow: 'ellipsis',
    },
  },
};

type FormSchema = StrictRJSFSchema & {
  properties: {
    defaultField: RJSFSchema;
  };
};

interface IAttributeUpdateComponent {
  attribs: ICustomAttributeDefinitionWithStaged;
  onValueChange?: (
    attribDefinitionId: number,
    value: unknown,
    isValid: boolean,
    isUpdated: boolean
  ) => void;
  disableForm: boolean;
}

const formTheme = createTheme({
  typography: {
    fontSize: 18,
  },
  components: {
    MuiTextField: {
      defaultProps: {
        variant: 'standard',
      },
      styleOverrides: {
        root: {},
      },
    },
    MuiGrid: {
      styleOverrides: {
        root: {
          margin: '0 !important',
          padding: '0 !important',
        },
      },
    },
    MuiFormGroup: {
      styleOverrides: {
        root: {
          border: 'solid 1px red',
          margin: '0px !important',
        },
      },
    },
  },
});

const AttributeUpdateComponent: FC<IAttributeUpdateComponent> = ({
  attribs,
  onValueChange,
  disableForm,
}: IAttributeUpdateComponent) => {
  const {
    defaultValue,
    id,
    schema: attributeSchema,
    isUserEditable,
    schemaDefaultValue,
    pendingApproval,
  } = attribs;
  const [{ userFormData, enableValidate }, setStates] = useMergeState<{
    userFormData: unknown;
    enableValidate: boolean;
  }>({
    userFormData: undefined,
    enableValidate: false,
  });
  const uiSchema = {
    defaultField: {
      'ui:emptyValue': '',
      'ui:options': {
        label: false,
      },
    },
    'ui:submitButtonOptions': {
      norender: true,
    },
  };

  const getTypedValue = (dataType, dataValue) => {
    if (dataType === 'boolean') {
      return /^(true)$/i.test(dataValue);
    }
    return dataValue;
  };

  const formSchema: FormSchema = useMemo(() => {
    let schemaData: FormSchema = {
      type: 'object',
      properties: { defaultField: {} },
    };
    try {
      const parsedSchema: StrictRJSFSchema = JSON.parse(
        attributeSchema as string
      );
      schemaData = {
        ...(parsedSchema.type === 'number' && { required: ['defaultField'] }),
        properties: {
          defaultField: {
            ...parsedSchema,
            ...{
              type: parsedSchema.type || 'string',
              default: getTypedValue(
                parsedSchema.type || 'string',
                defaultValue || parsedSchema.default
              ),
              title: '',
            },
          },
        },
      };
    } catch (err) {
      schemaData = {
        type: 'object',
        properties: {
          defaultField: {
            title: '',
            type: 'string',
            default: defaultValue || '',
          },
        },
      };
    }
    return schemaData;
  }, [attributeSchema]);

  const debounceValueChangeHandler = useDebounce(onValueChange, 100);
  const handleOnChange = (
    changeData: IChangeEvent<Record<string, unknown>, any, unknown>
  ): void => {
    const { formData, errors, edit, schema } = changeData;
    const formValue = formData?.defaultField;
    const isValid = errors?.length === 0;

    if (edit === undefined || !isUserEditable) {
      setStates({ enableValidate: false });
      return null;
    }
    setStates({ enableValidate: true });

    let isUpdated = edit;
    if (schema?.properties?.defaultField.default !== undefined) {
      isUpdated = schema?.properties?.defaultField.default !== formValue;
    }

    if (!isUpdated) {
      onValueChange(id, formValue, isValid, isUpdated);
      return null;
    }

    // Only debounce if it's edited
    debounceValueChangeHandler(id, formValue, isValid, isUpdated);
    return null;
  };

  useEffect(() => {
    handleOnChange({ formData: userFormData } as IChangeEvent<
      Record<string, unknown>,
      RJSFSchema,
      unknown
    >);
  }, []);

  const customErrorMessages = {
    required: () => 'must have required property',
  };

  function transformErrors(
    errors: RJSFValidationError[]
  ): RJSFValidationError[] {
    return errors.map(error => {
      const { name } = error;
      const customMessageFn = customErrorMessages[name];

      if (customMessageFn) {
        const params = error.params || {};
        const customMessage = customMessageFn({ params });
        return { ...error, message: customMessage };
      }

      return error;
    });
  }

  const handleOnBlur = () => {
    const fieldType = formSchema.properties.defaultField.type ?? 'string';
    const fieldDefault = schemaDefaultValue ?? '';
    const currentValue =
      (userFormData as { defaultField: unknown })?.defaultField ?? '';

    const isClearable = fieldType === 'string' || fieldType === 'number';
    const hasValue = currentValue !== '' && currentValue !== null;
    const hasDefaultValue =
      fieldDefault !== '' &&
      fieldDefault !== null &&
      fieldDefault !== undefined;

    if (isClearable && !hasValue && hasDefaultValue && isUserEditable) {
      setStates({
        userFormData: { defaultField: fieldDefault },
      });
      onValueChange(id, fieldDefault, true, true);
    }
  };

  return (
    <ThemeProvider theme={formTheme}>
      <Box
        sx={{
          padding: 0,
          margin: 0,
          '& .form-group': {
            margin: 0,
          },
          '& .form-group.field.field-number label.Mui-required': {
            display: 'none',
          },
        }}
      >
        <Form
          disabled={disableForm}
          readonly={!isUserEditable || pendingApproval}
          autoComplete='off'
          onBlur={handleOnBlur}
          onChange={data => {
            // Should set the state first before execute handler.
            setStates({ userFormData: data.formData });
            handleOnChange(data);
          }}
          // eslint-disable-next-line react/jsx-no-bind
          transformErrors={transformErrors}
          formData={userFormData}
          schema={formSchema}
          uiSchema={uiSchema}
          validator={validator}
          showErrorList={false}
          onSubmit={() => {}}
          liveValidate={enableValidate}
          noHtml5Validate
          omitExtraData
          liveOmit
        />
      </Box>
    </ThemeProvider>
  );
};

const BulkEditorFormRenderer: FC<IProps> = ({
  attributes,
  onValueUpdate,
  disableForm,
  customAttributeIds,
  isBulkUpdate = true,
}: IProps) => {
  const [updatedAttribs, setUpdatedAttribs] = useState<{
    [key: string]: boolean;
  }>({});
  const groupName = useMemo(
    () => attributes.find(i => i.groupName)?.groupName,
    [attributes]
  );

  const handleAttributeUpdate = (
    attribDefinitionId: number,
    value: unknown,
    isValid: boolean,
    isUpdated: boolean
  ) => {
    setUpdatedAttribs({
      ...updatedAttribs,
      [attribDefinitionId]: isUpdated,
    });

    onValueUpdate(attribDefinitionId, value, isValid, isUpdated);
  };

  const isCustomAttribute = (id: number) => {
    const filterId =
      customAttributeIds.filter(attrId => id === Number(attrId)).length > 0;
    return filterId;
  };

  return (
    <>
      <Box sx={{ display: 'flex', gap: 2, pt: 1 }}>
        {groupName !== 'RDM Fields' ? (
          <Typography sx={{ fontSize: '12px', fontWeight: 'bold', my: 0 }}>
            {groupName}
          </Typography>
        ) : null}
      </Box>
      {orderBy(attributes, ['name']).map(
        (attribs: ICustomAttributeDefinitionWithStaged) => (
          <Grid
            key={attribs.id}
            container
            columnSpacing={2}
            columns={12}
            sx={{
              ...(updatedAttribs[attribs.id]
                ? { backgroundColor: 'common.lightSurfaceVariant' }
                : {}),
              ':hover': { backgroundColor: 'common.rowHover' },
              cursor: 'pointer',
              width: 'calc(100% + 32px)',
              padding: '8px 16px',
              marginLeft: '-16px',
            }}
          >
            <Grid item xs={2} sm={2} md={3}>
              <Box
                display='flex'
                flexDirection='column'
                sx={{
                  ...customStyles.ellipsis,
                  height: '100%',
                  justifyContent: 'center',
                }}
              >
                <Box display='flex' flexDirection='row' alignItems='center'>
                  <Tooltip arrow placement='top-start' title={attribs.name}>
                    <Typography
                      sx={{
                        fontSize: '12px',
                        color:
                          ((attribs.pendingApproval ||
                            !attribs.isUserEditable) &&
                            '#DADADA') ||
                          (updatedAttribs[attribs.id] && 'common.textGreen') ||
                          '',
                        padding: '0px 5px 0px 0px',
                      }}
                    >
                      {attribs.name}
                    </Typography>
                  </Tooltip>
                </Box>
              </Box>
            </Grid>
            <Grid item xs sm={8} md={6} lg={4}>
              <Box sx={{ paddingTop: '12px', padding: 0, margin: 0 }}>
                <AttributeUpdateComponent
                  onValueChange={handleAttributeUpdate}
                  attribs={attribs}
                  disableForm={disableForm}
                />
              </Box>
            </Grid>
            <Grid
              item
              sx={{ flexGrow: 1, display: { xs: 'none', sm: 'block' } }}
            />
            <Grid item sx={{ flexShrink: 1, width: '60px' }}>
              <Box
                display='flex'
                flexDirection='row'
                sx={{
                  height: '100%',
                  justifyContent: 'right',
                  gap: 1,
                  pt: 1,
                }}
              >
                {!isBulkUpdate && isCustomAttribute(attribs.id) && (
                  <Tooltip arrow placement='top-start' title='Custom Attribute'>
                    <img
                      src='assets/asset-management/settings.png'
                      height='14px'
                      width='14px'
                      alt='custom-attribute'
                    />
                  </Tooltip>
                )}
                {attribs.isSecret && (
                  <Tooltip arrow placement='top' title='Secret'>
                    <LockOutlined
                      sx={{
                        fontSize: 'medium',
                        mt: '2px',
                        color: 'common.closeButton',
                      }}
                    />
                  </Tooltip>
                )}
                {attribs.doesStagedValueExist && (
                  <Tooltip arrow placement='top' title='Site transfer pending'>
                    <Sync
                      sx={{
                        fontSize: 'medium',
                        mt: '2px',
                        color: 'common.closeButton',
                      }}
                    />
                  </Tooltip>
                )}
                {!attribs.isUserEditable && (
                  <Tooltip arrow placement='top' title='Read only'>
                    <DoDisturb
                      sx={{
                        fontSize: '12px',
                        mt: '2px',
                        color: 'common.closeButton',
                      }}
                    />
                  </Tooltip>
                )}
                {attribs.pendingApproval && (
                  <Tooltip arrow placement='top' title='Approval is pending'>
                    <PendingActionsOutlined
                      sx={{
                        fontSize: '12px',
                        mt: '2px',
                        color: 'common.closeButton',
                      }}
                    />
                  </Tooltip>
                )}
              </Box>
            </Grid>
          </Grid>
        )
      )}
    </>
  );
};

export default BulkEditorFormRenderer;
