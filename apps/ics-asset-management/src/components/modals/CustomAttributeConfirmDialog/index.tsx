import React, { memo, useCallback, useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, Dialog, Typography, Divider } from '@mui/material';
import { LoadingButton } from '@mui/lab';
import { useSnackbar } from 'notistack';
import chunk from 'lodash/chunk';
import isEqual from 'lodash/isEqual';
import { WarningAmber } from '@mui/icons-material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import useHasPermissions from '../../../hooks/useHasPermissions';
import { useUpdateCustomAttributesByEntityType } from '../../../services/attribute-update-use-query';
import { IUpdateCustomAttributeDeploymentType } from '../../../constants/types';
import sleep from '../../../utils/sleep';
import UserRoles from '../../../constants/userRoles';
import FeatureFlags from '../../../constants/featureFlags';
import ScheduleDeploymentModal from '../../ScheduleDeploymentModal';
import BulkUpdateWarningDialog from '../BulkUpdateWarning';
import {
  useGetMaintenanceWindow,
  usePutApprovalReject,
} from '../../../services/use-query';
import TwoFactorVerification from '../../TwoFactorAuthentication/TwoFactorAuthentication';
import {
  CustomAttributeConfirmDialogProps,
  CustomAttributeConfirmDialogStep,
  CustomAttributeConfirmDialogState,
  TimeAndDateFormatInScheduleWindowType,
  CustomAttributeConfirmDialogType,
} from './types';
import {
  CustomAttributeConfirmDialogText,
  CustomAttributeConfirmDialogContent,
} from './constants';

const CustomAttributeConfirmDialog = memo(
  ({
    attributes,
    canSubmit,
    defaultDeploymentType,
    entityType,
    isLoading,
    onSubmit,
    siteIds,
    isBulkUpdate,
    dailogType = CustomAttributeConfirmDialogType.UPDATE,
    showMfaPrompt = true,
    attributesToApprove = [],
  }: CustomAttributeConfirmDialogProps) => {
    const [confirmDialogState, setConfirmDialogState] =
      useState<CustomAttributeConfirmDialogState>({
        isDialogOpen: false,
        isMutating: false,
        hasMutationError: false,
        step: CustomAttributeConfirmDialogStep.confirmation,
      });
    const { enqueueSnackbar } = useSnackbar();
    const hasImmediateDeploymentAccess = useHasPermissions({
      userRoles: [UserRoles.CONFIG_MGMT_DEPLOY],
      companyFeatureFlags: [FeatureFlags.CONFIG_MGMT],
    });

    const { hasMutationError, isDialogOpen, isMutating, step } =
      confirmDialogState;

    const { data: NextMaintenanceIntimationWindow } = useGetMaintenanceWindow(
      isDialogOpen &&
        step === CustomAttributeConfirmDialogStep.confirmationMaintainance
    );

    const {
      title,
      description,
      primaryAction,
      secondaryAction,
      scheduleAction,
    } = CustomAttributeConfirmDialogText[step];

    const [isScheduleDialogOpen, setIsScheduleDialogOpen] = useState(false);
    const [isScheduleTriggered, setIsScheduleTriggered] = useState(false);
    const [showLimitWarning, setShowLimitWarning] = useState(false);
    const [isTwoFactorOpen, setTwoFactorOpen] = useState(false);
    const [mfaCode, setMfaCode] = useState('');

    const closeWarningDialog = useCallback(() => {
      setShowLimitWarning(false);
    }, [setShowLimitWarning]);

    const openScheduleDialog = useCallback(() => {
      setIsScheduleDialogOpen(true);
    }, [setIsScheduleDialogOpen]);

    const closeScheduleDialog = useCallback(() => {
      setIsScheduleDialogOpen(false);
    }, [setIsScheduleDialogOpen]);

    const hasSchedulePermission = useHasPermissions({
      userRoles: [UserRoles.CONFIG_MGMT_DEPLOY],
      companyFeatureFlags: [FeatureFlags.SCHEDULING_UCA],
    });

    const { mutate: submitUpdateCustomAttributesByEntityType } =
      useUpdateCustomAttributesByEntityType({
        onSuccess: (success: any) => {
          if (isScheduleTriggered && success?.message) {
            enqueueSnackbar(success.message, { variant: 'success' });
            setIsScheduleTriggered(false);
          } else {
            enqueueSnackbar('MFA code verified successfully', {
              variant: 'success',
              autoHideDuration: 5000,
            });
          }
        },
        onError: (error: any) => {
          if (error?.response?.status === 403) {
            enqueueSnackbar(error?.response?.data?.message, {
              variant: 'error',
              autoHideDuration: 5000,
            });
          } else if (isScheduleTriggered && error?.response?.data?.message) {
            enqueueSnackbar(error.response.data.message, { variant: 'error' });
            setIsScheduleTriggered(false);
          }
        },
      });

    // approve the request for custom attributes
    const { mutate: putApprovalRejectMutate } = usePutApprovalReject();
    const handleApprove = useCallback(
      (deploymentType: IUpdateCustomAttributeDeploymentType) => {
        // TO-DO : Remove any
        const payload = attributesToApprove.map((attribute: any) => ({
          id: Number(attribute.entityApprovalSettingId),
          status: 'approved',
          comment: null,
          additionalData: {
            approver: {
              deploymentType,
            },
          },
        }));

        putApprovalRejectMutate(payload, {
          onSuccess: () => {
            enqueueSnackbar(
              'The request for modifying site attributes request has been approved. Submitter will be notified of its status.',
              { variant: 'success' }
            );

            setConfirmDialogState({
              isDialogOpen: false,
              isMutating: false,
              hasMutationError: false,
              step: CustomAttributeConfirmDialogStep.confirmation,
            });
            onSubmit(5000);
          },
          onError: (error: any) => {
            enqueueSnackbar(
              `Approve failed: ${error.message || 'Unknown error'}`,
              { variant: 'error' }
            );

            setConfirmDialogState({
              isDialogOpen: false,
              isMutating: false,
              hasMutationError: false,
              step: CustomAttributeConfirmDialogStep.confirmation,
            });
          },
        });
      },
      []
    );

    const handleSubmitUpdateCustomAttributesByEntityType = useCallback(
      async (data, code?: string) => {
        setConfirmDialogState(
          (prevState: CustomAttributeConfirmDialogState) => ({
            ...prevState,
            isMutating: true,
          })
        );
        const BATCH_SIZE = 50_000;
        const numAttribs = attributes.length;
        const numSites = siteIds?.length;

        if (isBulkUpdate && numAttribs * numSites > BATCH_SIZE) {
          setConfirmDialogState(
            (prevState: CustomAttributeConfirmDialogState) => ({
              ...prevState,
              isMutating: false,
            })
          );
          setShowLimitWarning(true);
          return;
        }

        const numSitesPerBatch = Math.floor(BATCH_SIZE / numAttribs);
        const batches = chunk(siteIds, numSitesPerBatch);
        data.deploymentType === IUpdateCustomAttributeDeploymentType.scheduled
          ? setIsScheduleTriggered(true)
          : setIsScheduleTriggered(false);

        if (dailogType === CustomAttributeConfirmDialogType.APPROVE) {
          handleApprove(data.deploymentType);
        } else {
          const allBatches = await Promise.allSettled(
            batches.map(entityIds =>
              submitUpdateCustomAttributesByEntityType({
                entityType,
                payload: {
                  attributes,
                  entityIds,
                  ...data,
                  mfaCode: code,
                },
              })
            )
          );

          const allSuccess = allBatches.every(
            item => item.status === 'fulfilled'
          );

          await sleep(500);

          if (isScheduleTriggered) {
            enqueueSnackbar(
              allSuccess
                ? CustomAttributeConfirmDialogContent.successSnackBar
                : CustomAttributeConfirmDialogContent.errorSnackBar,
              {
                variant: allSuccess ? 'success' : 'error',
              }
            );
          }

          if (allSuccess) {
            onSubmit(500);
          }

          setConfirmDialogState({
            isDialogOpen: !allSuccess,
            isMutating: !allSuccess,
            hasMutationError: !allSuccess,
            step: allSuccess
              ? CustomAttributeConfirmDialogStep.confirmation
              : step,
          });
        }
      },
      [
        attributes,
        entityType,
        siteIds,
        confirmDialogState,
        setConfirmDialogState,
        step,
      ]
    );

    const scheduleUpdateAttr = (scheduleData: any) => {
      handleSubmitUpdateCustomAttributesByEntityType(scheduleData);
    };

    const handlePrimaryAction = useCallback(
      (code?: string) => {
        const currentCode = code || mfaCode;
        switch (step) {
          case CustomAttributeConfirmDialogStep.confirmation:
            setConfirmDialogState(
              (prevState: CustomAttributeConfirmDialogState) => ({
                ...prevState,
                step: CustomAttributeConfirmDialogStep.confirmationImmediate,
              })
            );

            break;
          case CustomAttributeConfirmDialogStep.confirmationImmediate:
            if (showMfaPrompt && !currentCode) {
              setMfaCode('');
              setTwoFactorOpen(true);
            } else {
              handleSubmitUpdateCustomAttributesByEntityType(
                {
                  deploymentType:
                    IUpdateCustomAttributeDeploymentType.immediate,
                },
                currentCode
              );
              setMfaCode('');
            }
            break;
          case CustomAttributeConfirmDialogStep.confirmationMaintainance:
            if (showMfaPrompt && !currentCode) {
              setMfaCode('');
              setTwoFactorOpen(true);
            } else if (NextMaintenanceIntimationWindow?.isNext) {
              setConfirmDialogState(
                (prevState: CustomAttributeConfirmDialogState) => ({
                  ...prevState,
                  step: CustomAttributeConfirmDialogStep.scheduleWindow,
                })
              );
            } else {
              handleSubmitUpdateCustomAttributesByEntityType(
                {
                  deploymentType:
                    defaultDeploymentType ??
                    IUpdateCustomAttributeDeploymentType.maintenanceWindow,
                },
                currentCode
              );
              setMfaCode('');
            }
            break;
          case CustomAttributeConfirmDialogStep.scheduleWindow:
            handleSubmitUpdateCustomAttributesByEntityType(
              {
                deploymentType:
                  defaultDeploymentType ??
                  IUpdateCustomAttributeDeploymentType.maintenanceWindow,
              },
              currentCode
            );
            setMfaCode('');
            break;
          default:
            handleSubmitUpdateCustomAttributesByEntityType({
              deploymentType:
                defaultDeploymentType ??
                IUpdateCustomAttributeDeploymentType.maintenanceWindow,
            });
            break;
        }
      },
      [step, attributes]
    );

    const handleSecondaryAction = useCallback(async () => {
      switch (step) {
        case CustomAttributeConfirmDialogStep.confirmation:
          setConfirmDialogState(
            (prevState: CustomAttributeConfirmDialogState) => ({
              ...prevState,
              step: CustomAttributeConfirmDialogStep.confirmationMaintainance,
            })
          );
          break;
        default:
          setConfirmDialogState(
            (prevState: CustomAttributeConfirmDialogState) => ({
              ...prevState,
              step: CustomAttributeConfirmDialogStep.confirmation,
            })
          );
          break;
      }
    }, [step, attributes]);

    const handleCloseDialog = useCallback(() => {
      setConfirmDialogState({
        hasMutationError: false,
        isDialogOpen: false,
        isMutating: false,
        step: CustomAttributeConfirmDialogStep.confirmation,
      });
    }, [confirmDialogState, setConfirmDialogState]);

    const handleOpenDialog = useCallback(async () => {
      if (hasImmediateDeploymentAccess) {
        setConfirmDialogState(
          (prevState: CustomAttributeConfirmDialogState) => ({
            ...prevState,
            isDialogOpen: true,
          })
        );
        return;
      }
      await handleSubmitUpdateCustomAttributesByEntityType({
        deploymentType: IUpdateCustomAttributeDeploymentType.maintenanceWindow,
      });
    }, [
      attributes,
      confirmDialogState,
      setConfirmDialogState,
      hasImmediateDeploymentAccess,
    ]);

    const timeAndDateFormatInScheduleWindow: TimeAndDateFormatInScheduleWindowType =
      (from, to) => {
        // Helper function to format time
        const formatTime = (date: string | Date | undefined) =>
          new Date(date as string)
            .toLocaleString('en-GB', {
              hour: '2-digit',
              minute: '2-digit',
              hour12: true,
              timeZone: 'GMT',
            })
            .replace(' ', '');

        // Helper function to format date
        const formatDate = (date: string | Date | undefined) =>
          new Date(date as string).toLocaleDateString('en-GB', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            timeZone: 'GMT',
          });

        return `Est. ${formatTime(from)} - ${formatTime(to)} GMT ${formatDate(
          from
        )}`;
      };

    const handleCancelTwoFactor = () => {
      setTwoFactorOpen(false);
    };

    const dialogStyles =
      step === CustomAttributeConfirmDialogStep.scheduleWindow
        ? {
            borderRadius: '16px',
            width: '650px',
            height: '190px',
            padding: 3,
            display: 'flex',
            justifyContent: 'space-between',
            backgroundColor: 'common.modalBackground',
            boxSizing: 'unset',
            overflow: 'hidden',
            maxWidth: 'none',
          }
        : {
            borderRadius: '20px',
            width: '650px',
            padding: 3,
            display: 'flex',
            justifyContent: 'space-between',
            backgroundColor: 'common.modalBackground',
            boxSizing: 'unset',
            overflow: 'hidden',
            maxWidth: 'none',
          };

    const BUTTON_STYLES = {
      UPDATE: {
        minWidth: '100px',
      },
      SUBMIT_FOR_APPROVAL: {
        minWidth: '100px',
      },
      APPROVE: {
        minWidth: '100px',
        m: 0,
        fontSize: '12px',
        color: '#3f51b5',
        border: '1px solid #00394D',
        backgroundColor: 'transparent',

        '&:hover': {
          backgroundColor: '#3f51b50a',
        },
      },
    };

    const BUTTON_TEXT = {
      UPDATE: CustomAttributeConfirmDialogContent.openUpdateDialogButton,
      SUBMIT_FOR_APPROVAL:
        CustomAttributeConfirmDialogContent.openSubmitForApprovalDialogButton,
      APPROVE: CustomAttributeConfirmDialogContent.openApproveDialogButton,
    };

    const BUTTON_END_ICON = {
      UPDATE: null,
      SUBMIT_FOR_APPROVAL: null,
      APPROVE: <CheckCircleIcon fontSize='small' />,
    };

    return (
      <>
        {!isTwoFactorOpen && (
          <Dialog
            open={isDialogOpen}
            PaperProps={{
              sx: dialogStyles,
            }}
          >
            <Box
              display='flex'
              flexDirection='column'
              gap={
                step === CustomAttributeConfirmDialogStep.scheduleWindow ? 1 : 2
              }
            >
              <Box
                display='flex'
                flexDirection='column'
                gap={
                  step === CustomAttributeConfirmDialogStep.scheduleWindow
                    ? 1
                    : 2
                }
              >
                <Box display='flex' alignItems='center' gap={1}>
                  <WarningAmber sx={{ color: 'orange', fontSize: '24px' }} />
                  <Typography
                    variant='h6'
                    sx={
                      step === CustomAttributeConfirmDialogStep.scheduleWindow
                        ? {
                            fontFamily: 'Roboto',
                            fontWeight: 500,
                            fontSize: '16px',
                            lineHeight: '24px',
                            letterSpacing: '0.1px',
                          }
                        : {}
                    }
                  >
                    {title}
                  </Typography>
                </Box>
                {step === CustomAttributeConfirmDialogStep.scheduleWindow && (
                  <Divider />
                )}
                <Typography
                  variant='bodyMedium'
                  sx={
                    step === CustomAttributeConfirmDialogStep.scheduleWindow
                      ? {
                          fontFamily: 'Roboto',
                          fontWeight: 400,
                          fontSize: '14px',
                          lineHeight: '20px',
                          letterSpacing: '0.25px',
                          color: '#5D5D67',
                        }
                      : {}
                  }
                >
                  {description}
                  {step === CustomAttributeConfirmDialogStep.scheduleWindow &&
                    NextMaintenanceIntimationWindow && (
                      <Typography
                        sx={{
                          fontSize: '14px',
                          lineHeight: '20px',
                          paddingTop: '20px',
                        }}
                      >
                        {timeAndDateFormatInScheduleWindow(
                          NextMaintenanceIntimationWindow?.from,
                          NextMaintenanceIntimationWindow?.to
                        )}
                      </Typography>
                    )}
                </Typography>
                {step === CustomAttributeConfirmDialogStep.scheduleWindow && (
                  <Divider />
                )}
              </Box>

              <Box display='flex' justifyContent='space-between' gap={2}>
                {step !== CustomAttributeConfirmDialogStep.scheduleWindow && (
                  <Button
                    disabled={isMutating && !hasMutationError}
                    sx={{ minWidth: '100px' }}
                    onClick={handleCloseDialog}
                  >
                    {CustomAttributeConfirmDialogContent.closeDialogButton}
                  </Button>
                )}
                <Box
                  display='flex'
                  gap={2}
                  justifyContent='flex-end'
                  flexGrow={1}
                >
                  {hasSchedulePermission && scheduleAction && (
                    <>
                      <Button
                        disabled={isLoading}
                        sx={{ minWidth: '100px' }}
                        variant='outlined'
                        onClick={openScheduleDialog}
                      >
                        {scheduleAction}
                      </Button>
                      <ScheduleDeploymentModal
                        isOpen={isScheduleDialogOpen}
                        onScheduled={scheduleUpdateAttr}
                        close={closeScheduleDialog}
                      />
                    </>
                  )}

                  {step === CustomAttributeConfirmDialogStep.scheduleWindow ? (
                    <Button
                      disabled={isMutating && !hasMutationError}
                      sx={{ minWidth: '100px' }}
                      onClick={handleCloseDialog}
                    >
                      {CustomAttributeConfirmDialogContent.cancelDialogButton}
                    </Button>
                  ) : (
                    secondaryAction && (
                      <Button
                        onClick={handleSecondaryAction}
                        variant='outlined'
                        sx={{
                          whiteSpace: 'nowrap',
                        }}
                      >
                        {secondaryAction}
                      </Button>
                    )
                  )}
                  <Button
                    disabled={isMutating}
                    sx={{ minWidth: '100px', whiteSpace: 'nowrap' }}
                    variant='contained'
                    onClick={() => handlePrimaryAction(mfaCode)}
                  >
                    {primaryAction}
                  </Button>
                </Box>
              </Box>
            </Box>
          </Dialog>
        )}
        <TwoFactorVerification
          open={isTwoFactorOpen}
          onClose={handleCancelTwoFactor}
          onSubmit={async code => {
            setTwoFactorOpen(false);
            setMfaCode(code);
            handlePrimaryAction(code);
          }}
        />
        <BulkUpdateWarningDialog
          isDialogOpen={showLimitWarning}
          closeDialog={closeWarningDialog}
        />
        <LoadingButton
          disabled={!canSubmit}
          loading={isLoading}
          onClick={handleOpenDialog}
          sx={BUTTON_STYLES[dailogType]}
          variant='contained'
          endIcon={BUTTON_END_ICON[dailogType]}
        >
          {BUTTON_TEXT[dailogType]}
        </LoadingButton>
      </>
    );
  },
  (prevProps, nextProps) =>
    prevProps.canSubmit === nextProps.canSubmit &&
    prevProps.isLoading === nextProps.isLoading &&
    isEqual(prevProps.attributes, nextProps.attributes) &&
    isEqual(prevProps.siteIds, nextProps.siteIds) &&
    prevProps.defaultDeploymentType === nextProps.defaultDeploymentType &&
    prevProps.entityType === nextProps.entityType &&
    prevProps.isBulkUpdate === nextProps.isBulkUpdate &&
    prevProps.dailogType === nextProps.dailogType &&
    prevProps.showMfaPrompt === nextProps.showMfaPrompt &&
    isEqual(prevProps.attributesToApprove, nextProps.attributesToApprove)
);

export default CustomAttributeConfirmDialog;
