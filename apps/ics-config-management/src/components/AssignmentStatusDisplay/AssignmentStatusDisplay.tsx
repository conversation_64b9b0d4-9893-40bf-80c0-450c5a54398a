import React, { use<PERSON>emo, ReactNode } from 'react';
import styled from '@emotion/styled';
import {
  Box,
  CircularProgress,
  Tooltip,
  Typography,
} from '@mui/material';
import pluralize from 'pluralize';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';

import {
  DeviceIconWhite,
  SiteIconWhite,
  SiteTagIconWhite,
  TenantIconWhite,
} from '../Icons';
import { hasApprovalRequirement } from '../../utils/helpers';
import { ConfigInstanceItemStats, EntityPending } from '../../constants/types';

const Tag = styled(Box)({
  borderRadius: '4px',
  padding: '2px 8px',
  display: 'flex',
  alignItems: 'center',
});

const TagText = styled(Typography)({
  fontSize: '12px',
  fontWeight: 500,
  marginLeft: '4px',
});

const AssignmentTag = ({
  count,
  name,
  icon,
}: {
  count: number;
  name: string;
  icon: ReactNode;
}) =>
  count > 0 && (
    <Tooltip title={`${count} ${pluralize(name, count)} is under assignment`}>
      <Tag
        bgcolor='#045529'
        sx={{
          borderRadius: '12px !important',
          color: 'white !important',
        }}
      >
        <span
          style={{
            color: 'white !important',
          }}
        >
          {icon}
        </span>
        <TagText color='white'>Deployed</TagText>
      </Tag>
    </Tooltip>
  );

const ApprovalTag = ({ configFile }: { configFile: string }) => {
  if (!hasApprovalRequirement(configFile)) return null;

  return (
    <Tooltip title='The configuration file requires approval before any changes are deployed'>
      <Tag
        bgcolor='#005E80'
        sx={{
          borderRadius: '12px !important',
          color: 'white !important',
          display: 'flex',
          alignItems: 'center',
          height: 24,
        }}
      >
        <span
          style={{
            color: 'white !important',
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <InfoOutlinedIcon sx={{ fontSize: 14, color: 'white' }} />
        </span>
        <TagText
          color='white'
          sx={{ fontSize: 12, fontWeight: 500 }}
          width='115px'
        >
          Pending Approval
        </TagText>
      </Tag>
    </Tooltip>
  );
};

export interface AssignmentStatusDisplayProps {
  instanceId: string;
  configFile?: string;
  stats?: ConfigInstanceItemStats;
  instanceStats?: {
    tenants?: number;
    sites?: number;
    siteTags?: number;
    devices?: number;
  };
  isStatsLoading?: boolean;
  entityPendingObject?: EntityPending;
  approvalFlowStatus?: EntityPending[];
  isApprovalFlowDataLoading?: boolean;
}

const AssignmentStatusDisplay = ({
  instanceId,
  configFile = '',
  stats,
  instanceStats,
  isStatsLoading = false,
  entityPendingObject,
  approvalFlowStatus,
  isApprovalFlowDataLoading = false,
}: AssignmentStatusDisplayProps) => {
  const pendingApprovalsByInstanceId = useMemo(() => {
    if (!approvalFlowStatus?.length) return {};
    const pendingMap: Record<string, boolean> = {};

    approvalFlowStatus.forEach(item => {
      const configInstanceId = item.condition?.configInstanceId;
      const isPending = item.status === 'pending';

      if (configInstanceId && isPending) {
        pendingMap[configInstanceId] = true;
      }
    });

    return pendingMap;
  }, [approvalFlowStatus]);

  // Check if there's a pending approval for this instance
  const hasPendingApproval = useMemo(() => (
      pendingApprovalsByInstanceId[instanceId] ||
      entityPendingObject?.status === 'pending'
    ), [pendingApprovalsByInstanceId, instanceId, entityPendingObject]);

  // Extract assignment counts from stats or instanceStats
  const assignmentCounts = useMemo(() => {
    if (instanceStats) {
      return {
        tenants: instanceStats.tenants || 0,
        sites: instanceStats.sites || 0,
        siteTags: instanceStats.siteTags || 0,
        devices: instanceStats.devices || 0,
      };
    }

    if (stats) {
      return {
        tenants: 0, // ConfigInstanceItemStats doesn't have tenant count
        sites: 0, // ConfigInstanceItemStats doesn't have site count
        siteTags: 0, // ConfigInstanceItemStats doesn't have siteTag count
        devices: parseInt(stats.deviceCount, 10) || 0,
      };
    }

    return {
      tenants: 0,
      sites: 0,
      siteTags: 0,
      devices: 0,
    };
  }, [stats, instanceStats]);

  const isConfigAssigned = useMemo(() => {
    const { tenants, sites, siteTags, devices } = assignmentCounts;
    return tenants > 0 || sites > 0 || siteTags > 0 || devices > 0;
  }, [assignmentCounts]);

  if (isApprovalFlowDataLoading || isStatsLoading) {
    return <CircularProgress size={20} />;
  }

  return (
    <Box display='flex' alignItems='center' gap={1}>
      {hasPendingApproval && <ApprovalTag configFile={configFile} />}

      {!hasPendingApproval && isConfigAssigned && (
        <>
          <AssignmentTag
            icon={<TenantIconWhite fontSize='tag' />}
            name='tenant'
            count={assignmentCounts.tenants}
          />
          <AssignmentTag
            icon={<SiteIconWhite fontSize='tag' sx={{ fill: 'white' }} />}
            name='site'
            count={assignmentCounts.sites}
          />
          <AssignmentTag
            icon={<SiteTagIconWhite fontSize='tag' fill='#FFFFFF' />}
            name='site tag'
            count={assignmentCounts.siteTags}
          />
          <AssignmentTag
            icon={
              <DeviceIconWhite
                fontSize='tag'
                sx={{ '& svg path': { fill: 'white !important' } }}
              />
            }
            name='device'
            count={assignmentCounts.devices}
          />
        </>
      )}

      {!hasPendingApproval && !isConfigAssigned && (
        <Tooltip
          title='Not Assigned'
          sx={{
            height: '20px',
            borderRadius: '12px !important',
          }}
        >
          <Tag bgcolor='grey'>
            <TagText color='white' width='75px'>
              Not Assigned
            </TagText>
          </Tag>
        </Tooltip>
      )}
    </Box>
  );
};

export default AssignmentStatusDisplay;
